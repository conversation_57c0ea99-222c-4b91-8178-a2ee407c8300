#!/usr/bin/env python3
"""
Debug script to analyze the current Gmail signup page structure
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_gmail_signup():
    """Debug the Gmail signup page to understand current structure"""
    
    async with async_playwright() as p:
        # Launch browser in non-headless mode so we can see what's happening
        browser = await p.chromium.launch(
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-site-isolation-trials'
            ]
        )
        
        # Create new context
        context = await browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        # Create new page
        page = await context.new_page()
        
        try:
            print("🌐 Navigating to Gmail signup page...")
            await page.goto('https://accounts.google.com/signup')
            
            # Wait for page to load
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            print("📸 Taking screenshot of initial page...")
            await page.screenshot(path='debug_step1_initial.png')
            
            # Try to find first name field
            print("🔍 Looking for first name field...")
            first_name_selectors = [
                'input[name="firstName"]',
                'input[aria-label="First name"]',
                '[role="textbox"][name="First name"]',
                'input[type="text"]'
            ]
            
            for selector in first_name_selectors:
                try:
                    element = await page.locator(selector).first
                    if await element.is_visible():
                        print(f"✅ Found first name field with selector: {selector}")
                        break
                except:
                    continue
            else:
                print("❌ Could not find first name field")
            
            # Try to find last name field
            print("🔍 Looking for last name field...")
            last_name_selectors = [
                'input[name="lastName"]',
                'input[aria-label="Last name"]',
                '[role="textbox"][name="Last name (optional)"]',
                'input[type="text"]:nth-child(2)'
            ]
            
            for selector in last_name_selectors:
                try:
                    element = await page.locator(selector).first
                    if await element.is_visible():
                        print(f"✅ Found last name field with selector: {selector}")
                        break
                except:
                    continue
            else:
                print("❌ Could not find last name field")
            
            # Fill basic info and proceed to next step
            print("📝 Attempting to fill basic information...")
            try:
                await page.get_by_role('textbox', name='First name').fill('John')
                await asyncio.sleep(1)
                await page.get_by_role('textbox', name='Last name (optional)').fill('Doe')
                await asyncio.sleep(1)
                await page.get_by_role('button', name='Next').click()
                await asyncio.sleep(3)
                
                print("📸 Taking screenshot after basic info...")
                await page.screenshot(path='debug_step2_basic_info.png')
                
                # Look for birth date fields
                print("🔍 Looking for birth date fields...")
                await page.screenshot(path='debug_step3_birth_date.png')
                
                # Try to find month dropdown
                month_selectors = [
                    '[role="combobox"][name="Month"]',
                    'select[name="month"]',
                    '[aria-label="Month"]'
                ]
                
                for selector in month_selectors:
                    try:
                        element = await page.locator(selector).first
                        if await element.is_visible():
                            print(f"✅ Found month field with selector: {selector}")
                            break
                    except:
                        continue
                else:
                    print("❌ Could not find month field")
                
                # Try to find gender dropdown
                print("🔍 Looking for gender field...")
                gender_selectors = [
                    '[role="combobox"][name="Gender"]',
                    'select[name="gender"]',
                    '[aria-label="Gender"]'
                ]
                
                for selector in gender_selectors:
                    try:
                        element = await page.locator(selector).first
                        if await element.is_visible():
                            print(f"✅ Found gender field with selector: {selector}")
                            # Try to click and see options
                            await element.click()
                            await asyncio.sleep(1)
                            await page.screenshot(path='debug_gender_options.png')
                            break
                    except:
                        continue
                else:
                    print("❌ Could not find gender field")
                
            except Exception as e:
                print(f"❌ Error during form filling: {e}")
                await page.screenshot(path='debug_error.png')
            
            # Keep browser open for manual inspection
            print("🔍 Browser will stay open for 30 seconds for manual inspection...")
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await page.screenshot(path='debug_error_main.png')
        
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_gmail_signup())
