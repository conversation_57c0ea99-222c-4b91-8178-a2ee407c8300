"""
Gmail Auto Registration - Stealth Mode
Using Playwright with stealth mode to avoid detection
"""

import asyncio
import sys
import json
import click
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON>er, Page
from dotenv import load_dotenv
import random
import time
import logging

# Import our modules
from config import Config
from user_data_generator import UserDataGenerator
from proxy_manager import ProxyManager
from browser_profile import BrowserProfileGenerator
from human_behavior import HumanBehavior
from constants import Messages, Timeouts, Paths


class GmailRegistrationOrchestrator:
    """Main orchestrator cho toàn bộ Gmail registration system"""
    
    def __init__(self):
        self.config = Config
        self.proxy_manager = None
        self.user_generator = UserDataGenerator()
        self.browser_generator = BrowserProfileGenerator()
        self.results = []
        load_dotenv()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('registration.log'),
                logging.StreamHandler()
            ]
        )
        
    async def initialize(self) -> bool:
        """
        Initialize toàn bộ system
        
        Returns:
            bool: True nếu initialization thành công
        """
        try:
            print(Messages.CONSOLE["init_start"])
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            # Validate configuration
            if not self.config.validate_config():
                return False
            
            # Force disable proxy for testing
            self.config.USE_PROXY = False

            # Initialize proxy manager
            if self.config.USE_PROXY:
                self.proxy_manager = ProxyManager()
                if not await self.proxy_manager.initial_setup():
                    print(Messages.CONSOLE["proxy_failed"])
                    return False
            else:
                print(Messages.CONSOLE["proxy_disabled"])
            
            # Print configuration summary
            self.config.print_config_summary()
            
            print(Messages.CONSOLE["init_success"])
            return True
            
        except Exception as e:
            print(f"{Messages.CONSOLE['init_failed']}: {e}")
            return False
    
    async def register_single_account(self, account_id: int = 1) -> Dict[str, Any]:
        """
        Đăng ký một tài khoản Gmail
        
        Args:
            account_id: ID của tài khoản (for logging)
            
        Returns:
            Dict chứa kết quả đăng ký
        """
        start_time = datetime.now()
        print(f"\n{Messages.CONSOLE['registration_start']} #{account_id}")
        
        try:
            # Generate user data
            user_data = self.user_generator.generate_user_info()
            print(f"{Messages.CONSOLE['user_generated']}: {user_data['full_name']} ({user_data['username']})")
            
            # Get proxy if enabled
            proxy_config = None
            if self.proxy_manager:
                proxy_config = await self.proxy_manager.get_working_proxy()
                if not proxy_config:
                    return {
                        "success": False,
                        "error": Messages.ERRORS["no_proxy"],
                        "account_id": account_id,
                        "user_data": user_data,
                        "timestamp": start_time.isoformat()
                    }
                print(f"{Messages.CONSOLE['proxy_using']}: {proxy_config.server}")
            
            # Create browser profile
            browser_profile = self.browser_generator.create_human_browser_profile(proxy_config)
            print(f"🌍 Browser timezone: {browser_profile['timezone_id']}")
            print(f"📱 Viewport: {browser_profile['viewport']}")
            
            # Start registration process
            result = await self._perform_registration(user_data, browser_profile, account_id)
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            result["duration_seconds"] = duration
            result["account_id"] = account_id
            
            if result["success"]:
                print(f"{Messages.CONSOLE['registration_success']} #{account_id} in {duration:.1f}s")
                print(f"📧 Email: {result.get('email', 'N/A')}")
            else:
                print(f"{Messages.CONSOLE['registration_failed']} #{account_id}: {result.get('error', Messages.ERRORS['unknown_error'])}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": start_time.isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def _get_month_mapping(self) -> Dict[str, str]:
        """Get month mapping for different locales and formats"""
        return {
            # Standard month names
            "January": "January",
            "February": "February", 
            "March": "March",
            "April": "April",
            "May": "May",
            "June": "June",
            "July": "July",
            "August": "August",
            "September": "September",
            "October": "October",
            "November": "November",
            "December": "December",
            # Abbreviated month names
            "Jan": "January",
            "Feb": "February",
            "Mar": "March",
            "Apr": "April",
            "Jun": "June",
            "Jul": "July",
            "Aug": "August",
            "Sep": "September",
            "Oct": "October",
            "Nov": "November",
            "Dec": "December",
            # Numeric month values
            "1": "January",
            "2": "February",
            "3": "March",
            "4": "April",
            "5": "May",
            "6": "June",
            "7": "July",
            "8": "August",
            "9": "September",
            "10": "October",
            "11": "November",
            "12": "December"
        }
    
    async def _perform_registration(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Thực hiện quá trình đăng ký thực sự bằng Playwright

        Args:
            user_data: User data dict
            browser_profile: Browser profile dict
            account_id: Account ID

        Returns:
            Registration result dict
        """
        # Track username changes for logging
        original_username = user_data['username']
        username_conflict_history = []

        try:
            print(Messages.CONSOLE["browser_start"])
            
            async with async_playwright() as p:
                # Launch browser with stealth mode
                browser = await p.chromium.launch(
                    headless=False,  # Set to True for production
                    proxy=browser_profile.get('proxy'),
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-site-isolation-trials',
                        f'--window-size={browser_profile["viewport"]["width"]},{browser_profile["viewport"]["height"]}',
                        '--start-maximized'
                    ]
                )
                
                # Create new context with custom viewport and timezone
                context = await browser.new_context(
                    viewport=browser_profile['viewport'],
                    timezone_id=browser_profile['timezone_id'],
                    user_agent=browser_profile['user_agent'],
                    locale=browser_profile['locale'],
                    geolocation=browser_profile.get('geolocation'),
                    permissions=['geolocation']
                )
                
                # Add stealth scripts
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                """)
                
                # Create new page
                page = await context.new_page()
                
                try:
                    # Step 1: Navigate to Gmail signup page
                    logging.info("Step 1: Navigating to signup page")
                    await page.goto('https://accounts.google.com/signup')
                    await self._take_screenshot(page, account_id, "step1")
                    await self._random_delay()
                    
                    # Step 2: Fill basic information
                    logging.info("Step 2: Filling basic information")
                    
                    # Fill First name
                    await page.get_by_role('textbox', name='First name').fill(user_data['first_name'])
                    await self._random_delay()
                    
                    # Fill Last name
                    await page.get_by_role('textbox', name='Last name (optional)').fill(user_data['last_name'])
                    await self._random_delay()
                    
                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step2")
                    await self._random_delay()
                    
                    # Step 3: Fill personal information
                    logging.info("Step 3: Filling personal information")

                    # Select month
                    month_mapping = self._get_month_mapping()
                    user_month = month_mapping.get(str(user_data['birth_month']), str(user_data['birth_month']))
                    logging.info(f"Selecting month: {user_month} (from {user_data['birth_month']})")
                    await page.get_by_role('combobox', name='Month').click()
                    await page.get_by_role('option', name=user_month).click()
                    await self._random_delay()

                    # Enter day
                    logging.info(f"Entering day: {user_data['birth_day']}")
                    await page.get_by_role('textbox', name='Day').fill(str(user_data['birth_day']))
                    await self._random_delay()

                    # Enter year
                    logging.info(f"Entering year: {user_data['birth_year']}")
                    await page.get_by_role('textbox', name='Year').fill(str(user_data['birth_year']))
                    await self._random_delay()

                    # Select gender
                    logging.info("Opening gender dropdown")
                    await page.get_by_role('combobox', name='Gender').click()
                    await self._random_delay()

                    # Click on gender option with exact match - ensure proper capitalization
                    gender_value = user_data['gender']  # Should be "Male" or "Female"
                    logging.info(f"Selecting gender: {gender_value}")
                    await page.get_by_role('option', name=gender_value, exact=True).click()
                    await self._random_delay()

                    # Click Next
                    logging.info("Clicking Next after personal information")
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step3")
                    await self._random_delay()

                    # Wait for next page to load
                    await page.wait_for_load_state('networkidle', timeout=10000)
                    logging.info("Page loaded after personal information step")
                    
                    # Step 4: Fill username and password
                    logging.info("Step 4: Filling username and password")

                    # Check if we're on the "Choose your Gmail address" page (username suggestions)
                    page_title = await page.title()
                    current_url = page.url
                    logging.info(f"Current page: {page_title}")
                    logging.info(f"Current URL: {current_url}")

                    if "Choose your Gmail address" in page_title or "Pick a Gmail address" in page_title:
                        logging.info("On Gmail address selection page - looking for suggestions or create option")

                        # Look for "Create your own Gmail address" option
                        create_own_selectors = [
                            'text="Create your own Gmail address"',
                            'text="Create a different address"',
                            '[aria-label="Create your own Gmail address"]',
                            'button:has-text("Create")',
                            'text="Create"'
                        ]

                        create_clicked = False
                        for selector_str in create_own_selectors:
                            try:
                                logging.info(f"Trying create own selector: {selector_str}")
                                if selector_str.startswith('text='):
                                    text_content = selector_str.replace('text=', '').strip('"')
                                    selector = page.get_by_text(text_content)
                                elif 'aria-label' in selector_str:
                                    selector = page.locator(selector_str)
                                else:
                                    selector = page.locator(selector_str)

                                if await selector.is_visible(timeout=2000):
                                    await selector.click()
                                    create_clicked = True
                                    logging.info(f"Clicked create own address with: {selector_str}")
                                    await self._random_delay()
                                    break
                            except Exception as e:
                                logging.info(f"Create own selector {selector_str} failed: {str(e)}")
                                continue

                        if not create_clicked:
                            # If can't find create option, try to select a suggested username
                            logging.info("Could not find create option, looking for suggested usernames")
                            suggestion_selectors = [
                                'button[data-value*="@gmail.com"]',
                                '[role="button"]:has-text("@gmail.com")',
                                'button:has-text("available")',
                                '.username-suggestion',
                                'button[jsaction]'
                            ]

                            for selector_str in suggestion_selectors:
                                try:
                                    suggestions = page.locator(selector_str)
                                    count = await suggestions.count()
                                    if count > 0:
                                        first_suggestion = suggestions.first
                                        suggestion_text = await first_suggestion.text_content()
                                        logging.info(f"Selecting suggested username: {suggestion_text}")
                                        await first_suggestion.click()

                                        # Update username
                                        if '@gmail.com' in suggestion_text:
                                            user_data['username'] = suggestion_text.replace('@gmail.com', '')
                                        else:
                                            user_data['username'] = suggestion_text

                                        create_clicked = True
                                        break
                                except Exception as e:
                                    logging.info(f"Suggestion selector {selector_str} failed: {str(e)}")
                                    continue

                        if not create_clicked:
                            raise Exception("Could not find create option or username suggestions")

                        # Wait for page to load after selection
                        await page.wait_for_load_state('networkidle', timeout=10000)

                    # Now try to find username input field (for creating custom username)
                    username_selectors = [
                        'get_by_label("Create a Gmail address")',
                        'get_by_label("Username")',
                        'get_by_role("textbox", name="Username")',
                        'get_by_role("textbox", name="Create Gmail address")',
                        'get_by_placeholder("Username")',
                        'locator("input[name=\'username\']")',
                        'locator("input[type=\'text\']").last',
                        'locator("input[aria-label*=\'Gmail\']")'
                    ]

                    username_filled = False
                    for selector_str in username_selectors:
                        try:
                            logging.info(f"Trying username selector: {selector_str}")
                            if 'Create a Gmail address' in selector_str:
                                selector = page.get_by_label('Create a Gmail address')
                            elif selector_str.startswith('get_by_label') and 'Username' in selector_str:
                                selector = page.get_by_label('Username')
                            elif selector_str.startswith('get_by_role') and 'Username' in selector_str:
                                selector = page.get_by_role('textbox', name='Username')
                            elif selector_str.startswith('get_by_role') and 'Create Gmail' in selector_str:
                                selector = page.get_by_role('textbox', name='Create Gmail address')
                            elif selector_str.startswith('get_by_placeholder'):
                                selector = page.get_by_placeholder('Username')
                            elif 'name=\'username\'' in selector_str:
                                selector = page.locator('input[name="username"]')
                            elif 'Gmail' in selector_str:
                                selector = page.locator('input[aria-label*="Gmail"]')
                            else:
                                selector = page.locator('input[type="text"]').last

                            # Wait for element to be visible with shorter timeout
                            await selector.wait_for(state='visible', timeout=5000)
                            await selector.fill(user_data['username'])
                            username_filled = True
                            logging.info(f"Successfully filled username with selector: {selector_str}")
                            break
                        except Exception as e:
                            logging.info(f"Username selector {selector_str} failed: {str(e)}")
                            continue

                    if not username_filled:
                        raise Exception("Could not find username field with any selector")

                    await self._random_delay()

                    # Enhanced username conflict handling
                    username_conflict_result = await self._handle_username_conflict(page, user_data, account_id)
                    if username_conflict_result['username_changed']:
                        username_conflict_history.append(username_conflict_result)
                        logging.info(f"Username changed from '{username_conflict_result['original_username']}' to '{user_data['username']}' due to conflict")
                        await self._take_screenshot(page, account_id, "username_conflict_resolved")

                    # Click Next to proceed to password page
                    logging.info("Clicking Next to proceed to password page")
                    await page.get_by_role('button', name='Next').click()
                    await self._random_delay()

                    # Wait for page to load and check if we moved to password page
                    await page.wait_for_load_state('networkidle', timeout=10000)
                    current_url = page.url
                    logging.info(f"Current URL after Next click: {current_url}")

                    # Check if we're still on username page (indicates username might be taken)
                    if 'username' in current_url:
                        logging.warning("Still on username page - username might be taken or there's an error")

                        # Try to find and handle any error messages or suggestions
                        await asyncio.sleep(2)

                        # Look for any visible error messages
                        error_elements = page.locator('[role="alert"], .error-message, [data-error], .Ekjuhf')
                        error_count = await error_elements.count()
                        username_taken_detected = False

                        if error_count > 0:
                            for i in range(error_count):
                                error_text = await error_elements.nth(i).text_content()
                                logging.warning(f"Error message found: {error_text}")
                                if "taken" in error_text.lower() or "not available" in error_text.lower():
                                    username_taken_detected = True
                                elif "only letters" in error_text.lower() or "periods" in error_text.lower():
                                    # Username format error - fix the username
                                    logging.warning("Username format error detected, fixing username")
                                    original_username = user_data['username']
                                    # Remove invalid characters and replace with valid ones
                                    fixed_username = original_username.replace('_', '.').replace('-', '.')
                                    # Remove any other invalid characters
                                    import re
                                    fixed_username = re.sub(r'[^a-z0-9.]', '', fixed_username)

                                    # If username is too short after cleaning, add numbers
                                    if len(fixed_username) < 6:
                                        fixed_username += str(random.randint(100, 999))

                                    logging.info(f"Fixed username from {original_username} to {fixed_username}")

                                    # Clear and refill the username field
                                    username_field = page.get_by_label('Username')
                                    await username_field.clear()
                                    await username_field.fill(fixed_username)
                                    user_data['username'] = fixed_username

                                    # Click Next again
                                    await self._random_delay()
                                    await page.get_by_role('button', name='Next').click()
                                    await page.wait_for_load_state('networkidle', timeout=10000)
                                    current_url = page.url
                                    logging.info(f"URL after fixing username format: {current_url}")

                                    # Check if we moved to password page
                                    if 'password' in current_url or 'username' not in current_url:
                                        logging.info("Successfully moved to password page after fixing username")
                                        return  # Exit this error handling section
                                    else:
                                        logging.warning("Still on username page after fixing format")

                        # Also check for specific username taken messages
                        username_taken_messages = [
                            'That username is taken. Try another.',
                            'Username not available',
                            'This username is already taken',
                            'Choose a different username',
                            'Sorry, this username isn\'t available',
                            'Try a different username'
                        ]

                        for message in username_taken_messages:
                            try:
                                if await page.get_by_text(message).is_visible(timeout=1000):
                                    username_taken_detected = True
                                    logging.warning(f"Username taken message detected: {message}")
                                    break
                            except:
                                continue

                        if username_taken_detected:
                            # Use enhanced username conflict handling
                            secondary_conflict_result = await self._handle_username_conflict(page, user_data, account_id)
                            if secondary_conflict_result['username_changed']:
                                username_conflict_history.append(secondary_conflict_result)
                                logging.info(f"Secondary username conflict resolved: '{secondary_conflict_result['original_username']}' → '{user_data['username']}'")
                                await self._take_screenshot(page, account_id, "secondary_username_conflict_resolved")

                            # Click Next again after handling username taken
                            await self._random_delay()
                            await page.get_by_role('button', name='Next').click()
                            await page.wait_for_load_state('networkidle', timeout=10000)
                            current_url = page.url
                            logging.info(f"URL after handling username taken: {current_url}")
                        else:
                            logging.info("No username taken error detected, but still on username page")

                    logging.info("Password page loaded")

                    # Take screenshot of password page
                    await self._take_screenshot(page, account_id, "step4_password_page")

                    # Try multiple selectors for password fields
                    password_selectors = [
                        'locator("input[type=\'password\']")',
                        'get_by_label("Password")',
                        'get_by_placeholder("Password")',
                        'locator("input[name=\'password\']")'
                    ]

                    password_filled = False
                    for selector_str in password_selectors:
                        try:
                            logging.info(f"Trying password selector: {selector_str}")
                            if 'type=\'password\'' in selector_str:
                                password_fields = page.locator('input[type="password"]')
                                count = await password_fields.count()
                                if count >= 1:
                                    await password_fields.first.fill(user_data['password'])
                                    if count >= 2:
                                        await password_fields.last.fill(user_data['password'])
                                    password_filled = True
                                    logging.info(f"Successfully filled password fields (found {count} fields)")
                                    break
                            elif 'get_by_label' in selector_str:
                                selector = page.get_by_label('Password')
                                await selector.wait_for(state='visible', timeout=5000)
                                await selector.fill(user_data['password'])
                                password_filled = True
                                break
                            elif 'get_by_placeholder' in selector_str:
                                selector = page.get_by_placeholder('Password')
                                await selector.wait_for(state='visible', timeout=5000)
                                await selector.fill(user_data['password'])
                                password_filled = True
                                break
                            elif 'name=\'password\'' in selector_str:
                                selector = page.locator('input[name="password"]')
                                await selector.wait_for(state='visible', timeout=5000)
                                await selector.fill(user_data['password'])
                                password_filled = True
                                break
                        except Exception as e:
                            logging.info(f"Password selector {selector_str} failed: {str(e)}")
                            continue

                    if not password_filled:
                        raise Exception("Could not find password field with any selector")

                    await self._random_delay()

                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step4")
                    await self._random_delay()
                    
                    # Check for captcha
                    try:
                        # Check if we're on captcha/verification page
                        page_title = await page.title()
                        current_url = page.url

                        if "robot" in page_title.lower() or "captcha" in page_title.lower() or "verify" in page_title.lower():
                            logging.warning(f"Captcha/verification page detected: {page_title}")
                            await self._take_screenshot(page, account_id, "captcha_page")
                            return {
                                "success": False,
                                "error": f"Captcha/verification required: {page_title}",
                                "user_data": user_data,
                                "timestamp": datetime.now().isoformat()
                            }

                        # Also check for reCAPTCHA iframe
                        captcha_iframe = page.locator('iframe[title="reCAPTCHA"]')
                        if await captcha_iframe.count() > 0:
                            logging.warning("reCAPTCHA iframe detected")
                            await self._take_screenshot(page, account_id, "recaptcha")
                            return {
                                "success": False,
                                "error": "reCAPTCHA detected",
                                "user_data": user_data,
                                "timestamp": datetime.now().isoformat()
                            }
                    except Exception as e:
                        logging.info(f"Error checking for captcha: {str(e)}")
                    
                    # Check for success
                    if await page.get_by_text('Welcome to Google').is_visible():
                        email = f"{user_data['username']}@gmail.com"
                        result = {
                            "success": True,
                            "email": email,
                            "user_data": user_data,
                            "proxy_used": browser_profile.get('proxy'),
                            "timestamp": datetime.now().isoformat(),
                            "screenshots_path": str(self.config.SCREENSHOTS_DIR / f"account_{account_id}"),
                            "username_conflicts": {
                                "original_username": original_username,
                                "final_username": user_data['username'],
                                "username_changed": original_username != user_data['username'],
                                "conflict_history": username_conflict_history,
                                "total_conflicts": len(username_conflict_history)
                            }
                        }

                        # Log username change information
                        if original_username != user_data['username']:
                            logging.info(f"🔄 Username was changed during registration:")
                            logging.info(f"   Original: {original_username}")
                            logging.info(f"   Final: {user_data['username']}")
                            logging.info(f"   Total conflicts resolved: {len(username_conflict_history)}")
                            for i, conflict in enumerate(username_conflict_history, 1):
                                logging.info(f"   Conflict {i}: {conflict.get('resolution_method', 'unknown')} - {conflict.get('suggestions_found', 0)} suggestions found")
                        else:
                            logging.info(f"✅ Username '{original_username}' was available without conflicts")
                    else:
                        result = {
                            "success": False,
                            "error": "Registration failed or blocked",
                            "user_data": user_data,
                            "timestamp": datetime.now().isoformat(),
                            "username_conflicts": {
                                "original_username": original_username,
                                "final_username": user_data['username'],
                                "username_changed": original_username != user_data['username'],
                                "conflict_history": username_conflict_history,
                                "total_conflicts": len(username_conflict_history)
                            }
                        }
                    
                except Exception as e:
                    logging.error(f"Error during automation: {str(e)}")
                    logging.error(f"Error type: {type(e).__name__}")

                    # Take screenshot for debugging
                    await self._take_screenshot(page, account_id, "error")

                    # Try to get page title and URL for debugging
                    try:
                        page_title = await page.title()
                        page_url = page.url
                        logging.error(f"Error occurred on page: {page_title} ({page_url})")
                    except:
                        logging.error("Could not get page title/URL")

                    # Try to get page content for debugging
                    try:
                        page_content = await page.content()
                        # Save page HTML for debugging
                        html_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_error.html"
                        with open(html_path, 'w', encoding='utf-8') as f:
                            f.write(page_content)
                        logging.error(f"Page HTML saved to: {html_path}")
                    except:
                        logging.error("Could not save page HTML")

                    result = {
                        "success": False,
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "user_data": user_data,
                        "timestamp": datetime.now().isoformat(),
                        "username_conflicts": {
                            "original_username": original_username,
                            "final_username": user_data['username'],
                            "username_changed": original_username != user_data['username'],
                            "conflict_history": username_conflict_history,
                            "total_conflicts": len(username_conflict_history)
                        }
                    }
                
                finally:
                    await context.close()
                    await browser.close()
                
                await self._save_account_data(result)
                return result

        except Exception as e:
            logging.error(f"An error occurred during browser automation: {e}")
            logging.error(f"Error type: {type(e).__name__}")

            # Import traceback for detailed error info
            import traceback
            logging.error(f"Full traceback: {traceback.format_exc()}")

            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "user_data": user_data,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add random delay between actions to simulate human behavior"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def _take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot of current page state"""
        screenshot_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
        await page.screenshot(path=str(screenshot_path))
        logging.info(f"Screenshot saved: {screenshot_path}")

    async def _handle_username_conflict(self, page: Page, user_data: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Enhanced username conflict detection and resolution

        Args:
            page: Playwright page object
            user_data: User data dictionary containing username
            account_id: Account ID for logging

        Returns:
            Dict containing conflict resolution results
        """
        original_username = user_data['username']
        result = {
            'username_changed': False,
            'original_username': original_username,
            'final_username': original_username,
            'conflict_detected': False,
            'resolution_method': None,
            'suggestions_found': 0
        }

        try:
            # Wait for any error messages or suggestions to appear
            await asyncio.sleep(3)

            # Enhanced list of username taken error messages
            username_taken_messages = [
                'That username is taken. Try another.',
                'Username not available',
                'This username is already taken',
                'Choose a different username',
                'Sorry, this username isn\'t available',
                'Try a different username',
                'Username is not available',
                'This username isn\'t available',
                'Try another username',
                'Username already exists',
                'Please choose a different username'
            ]

            username_taken = False
            detected_message = None

            # Check for explicit error messages
            for message in username_taken_messages:
                try:
                    if await page.get_by_text(message).is_visible(timeout=1000):
                        username_taken = True
                        detected_message = message
                        logging.info(f"Username conflict detected: {message}")
                        break
                except:
                    continue

            # Check for visual indicators of username conflict
            if not username_taken:
                try:
                    # Look for error styling or suggestion containers
                    conflict_indicators = [
                        '[role="alert"]',
                        '.error-message',
                        '[data-error="true"]',
                        '.Ekjuhf',  # Google's error class
                        'button[data-value*="@gmail.com"]',
                        '[role="button"]:has-text("@gmail.com")',
                        'button:has-text("available")',
                        '.username-suggestion',
                        '[aria-describedby*="error"]'
                    ]

                    for indicator in conflict_indicators:
                        try:
                            elements = page.locator(indicator)
                            if await elements.count() > 0:
                                # Check if it's actually related to username conflict
                                for i in range(await elements.count()):
                                    element_text = await elements.nth(i).text_content()
                                    if element_text and any(keyword in element_text.lower()
                                                          for keyword in ['taken', 'available', 'try', 'choose', '@gmail.com']):
                                        username_taken = True
                                        detected_message = f"Visual indicator: {element_text[:50]}..."
                                        logging.info(f"Username conflict detected via indicator: {indicator}")
                                        break
                                if username_taken:
                                    break
                        except:
                            continue
                except:
                    pass

            if username_taken:
                result['conflict_detected'] = True
                logging.info(f"Username '{original_username}' is taken. Looking for suggestions...")

                # Take screenshot of conflict state
                await self._take_screenshot(page, account_id, "username_conflict_detected")

                # Try to find and select suggested usernames
                suggestion_success = await self._select_username_suggestion(page, user_data, result)

                if suggestion_success:
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved: '{original_username}' → '{user_data['username']}'")
                else:
                    # Fallback: generate new username
                    await self._generate_fallback_username(page, user_data, original_username, result)
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved with fallback: '{original_username}' → '{user_data['username']}'")

            return result

        except Exception as e:
            logging.error(f"Error in username conflict handling: {str(e)}")
            result['error'] = str(e)
            return result

    async def _select_username_suggestion(self, page: Page, user_data: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """
        Try to select a suggested username from Gmail's suggestions

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            result: Result dictionary to update

        Returns:
            True if suggestion was successfully selected, False otherwise
        """
        try:
            # Enhanced selectors for username suggestions
            suggestion_selectors = [
                'button[data-value*="@gmail.com"]',  # Most common
                '[role="button"][data-value*="@gmail.com"]',
                'button:has-text("@gmail.com")',
                '[role="button"]:has-text("@gmail.com")',
                'button:has-text("available")',
                '.username-suggestion button',
                '.suggestion-button',
                'button[jsaction*="click"]:has-text("@gmail.com")',
                'div[role="button"][data-value*="@gmail.com"]',
                '[data-value*="@gmail.com"][role="button"]',
                'button[aria-label*="available"]',
                '.VfPpkd-LgbsSe:has-text("@gmail.com")'  # Google Material button
            ]

            suggestions_found = 0
            selected_suggestion = None

            for selector in suggestion_selectors:
                try:
                    suggestions = page.locator(selector)
                    count = await suggestions.count()
                    suggestions_found = max(suggestions_found, count)

                    if count > 0:
                        logging.info(f"Found {count} username suggestions with selector: {selector}")

                        # Try to get all suggestions for logging
                        all_suggestions = []
                        for i in range(min(count, 5)):  # Limit to first 5 suggestions
                            try:
                                suggestion_text = await suggestions.nth(i).text_content()
                                if suggestion_text:
                                    all_suggestions.append(suggestion_text.strip())
                            except:
                                continue

                        if all_suggestions:
                            logging.info(f"Available username suggestions: {all_suggestions}")

                        # Select the first available suggestion
                        first_suggestion = suggestions.first
                        suggestion_text = await first_suggestion.text_content()

                        if suggestion_text:
                            suggestion_text = suggestion_text.strip()
                            logging.info(f"Selecting username suggestion: {suggestion_text}")

                            # Click the suggestion
                            await first_suggestion.click()
                            await asyncio.sleep(1)  # Wait for selection to register

                            # Extract username from suggestion
                            if '@gmail.com' in suggestion_text:
                                new_username = suggestion_text.replace('@gmail.com', '').strip()
                            else:
                                new_username = suggestion_text.strip()

                            # Update user data
                            user_data['username'] = new_username
                            selected_suggestion = suggestion_text

                            # Update result
                            result['resolution_method'] = 'suggestion_selected'
                            result['suggestions_found'] = suggestions_found
                            result['selected_suggestion'] = selected_suggestion

                            logging.info(f"✅ Successfully selected suggestion: {new_username}")
                            return True

                except Exception as e:
                    logging.debug(f"Suggestion selector {selector} failed: {str(e)}")
                    continue

            result['suggestions_found'] = suggestions_found
            if suggestions_found > 0:
                logging.warning(f"Found {suggestions_found} suggestions but couldn't select any")
            else:
                logging.warning("No username suggestions found")

            return False

        except Exception as e:
            logging.error(f"Error selecting username suggestion: {str(e)}")
            return False

    async def _generate_fallback_username(self, page: Page, user_data: Dict[str, Any], original_username: str, result: Dict[str, Any]) -> None:
        """
        Generate a fallback username when suggestions are not available

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            original_username: Original username that was taken
            result: Result dictionary to update
        """
        try:
            import random

            # Try multiple fallback strategies
            fallback_strategies = [
                lambda u: f"{u}{random.randint(100, 999)}",
                lambda u: f"{u}.{random.randint(10, 99)}",
                lambda u: f"{u}{random.randint(1000, 9999)}",
                lambda u: f"{u}.{random.randint(1, 9)}",
                lambda u: f"{u}{random.randint(10, 99)}",
            ]

            for i, strategy in enumerate(fallback_strategies):
                try:
                    new_username = strategy(original_username)
                    logging.info(f"Trying fallback username strategy {i+1}: {new_username}")

                    # Clear the username field and enter new username
                    username_selectors = [
                        'input[name="username"]',
                        'input[aria-label*="Username"]',
                        'input[aria-label*="Gmail"]',
                        'input[type="text"]:visible'
                    ]

                    username_field = None
                    for selector in username_selectors:
                        try:
                            field = page.locator(selector).first
                            if await field.is_visible():
                                username_field = field
                                break
                        except:
                            continue

                    if username_field:
                        await username_field.clear()
                        await username_field.fill(new_username)
                        user_data['username'] = new_username

                        result['resolution_method'] = f'fallback_strategy_{i+1}'
                        result['fallback_username'] = new_username

                        logging.info(f"✅ Generated fallback username: {new_username}")
                        return
                    else:
                        logging.warning("Could not find username field for fallback")

                except Exception as e:
                    logging.debug(f"Fallback strategy {i+1} failed: {str(e)}")
                    continue

            # Last resort: use original username with timestamp
            import time
            timestamp = str(int(time.time()))[-4:]  # Last 4 digits of timestamp
            final_username = f"{original_username}{timestamp}"
            user_data['username'] = final_username
            result['resolution_method'] = 'timestamp_fallback'
            result['fallback_username'] = final_username

            logging.info(f"✅ Using timestamp fallback username: {final_username}")

        except Exception as e:
            logging.error(f"Error generating fallback username: {str(e)}")
            # Keep original username as last resort
            result['resolution_method'] = 'no_change'

    async def register_batch_accounts(self, num_accounts: int, max_concurrent: int = None) -> List[Dict[str, Any]]:
        """
        Đăng ký nhiều tài khoản cùng lúc
        
        Args:
            num_accounts: Số lượng tài khoản cần đăng ký
            max_concurrent: Số lượng tối đa chạy đồng thời
            
        Returns:
            List kết quả đăng ký
        """
        if max_concurrent is None:
            max_concurrent = self.config.MAX_CONCURRENT_REGISTRATIONS
            
        print(f"\n🚀 Starting batch registration of {num_accounts} accounts")
        print(f"⚙️ Max concurrent: {max_concurrent}")
        print(f"⏱️ Delay between registrations: {self.config.DELAY_BETWEEN_REGISTRATIONS}s")
        
        # Create semaphore to limit concurrent registrations
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def register_with_semaphore(account_id: int):
            async with semaphore:
                result = await self.register_single_account(account_id)
                
                # Delay between registrations
                if account_id < num_accounts:
                    delay = self.config.DELAY_BETWEEN_REGISTRATIONS
                    print(f"⏰ Waiting {delay}s before next registration...")
                    await asyncio.sleep(delay)
                
                return result
        
        # Create tasks for all accounts
        tasks = []
        for i in range(1, num_accounts + 1):
            tasks.append(register_with_semaphore(i))
            
        # Run all tasks concurrently and wait for completion
        results = await asyncio.gather(*tasks)
        
        # Save batch results
        await self._save_batch_results(results)
        
        return results
    
    async def _save_account_data(self, account_data: Dict[str, Any]):
        """Save account data to JSON file"""
        try:
            # Create filename with timestamp and account ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_id = account_data.get("account_id", "unknown")
            filename = f"account_{account_id}_{timestamp}.json"
            
            # Save to accounts directory
            filepath = self.config.ACCOUNTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(account_data, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Account data saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save account data: {e}")
    
    async def _save_batch_results(self, results: List[Dict[str, Any]]):
        """Save batch results to JSON file"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
            
            # Save to results directory
            filepath = self.config.RESULTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Batch results saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save batch results: {e}")


@click.command()
@click.option('--accounts', '-n', default=1, help='Number of accounts to register')
@click.option('--concurrent', '-c', default=None, type=int, help='Max concurrent registrations')
@click.option('--test-mode', '-t', is_flag=True, help='Run in test mode (no actual registration)')
@click.option('--config-check', is_flag=True, help='Check configuration and exit')
@click.option('--proxy-test', is_flag=True, help='Test proxies and exit')
def main(accounts: int, concurrent: int, test_mode: bool, config_check: bool, proxy_test: bool):
    """Gmail Auto Registration Tool"""
    
    async def run_async():
        orchestrator = GmailRegistrationOrchestrator()
        
        # Initialize system
        if not await orchestrator.initialize():
            sys.exit(1)
            
        # Handle special modes
        if config_check:
            print("✅ Configuration check passed")
            return
            
        if proxy_test:
            if not orchestrator.proxy_manager:
                print("❌ Proxy testing requires proxy support to be enabled")
                return
            await orchestrator.proxy_manager.test_all_proxies()
            return
            
        if test_mode:
            print("🧪 Running in TEST MODE - no actual registrations will be performed")
            
        # Start registration process
        if accounts > 1:
            results = await orchestrator.register_batch_accounts(accounts, concurrent)
            success_count = len([r for r in results if r["success"]])
            print(f"\n✨ Batch registration completed: {success_count}/{accounts} successful")
        else:
            result = await orchestrator.register_single_account()
            if result["success"]:
                print("\n✨ Registration completed successfully!")
            else:
                print("\n❌ Registration failed!")
                sys.exit(1)
    
    # Run async code
    asyncio.run(run_async())


if __name__ == "__main__":
    main()