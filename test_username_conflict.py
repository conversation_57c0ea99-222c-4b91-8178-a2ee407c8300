#!/usr/bin/env python3
"""
Test script to demonstrate enhanced username conflict handling
"""

import asyncio
import logging
from main import GmailRegistrationOrchestrator

# Configure logging to see detailed output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('username_conflict_test.log'),
        logging.StreamHandler()
    ]
)

async def test_username_conflict_handling():
    """Test the enhanced username conflict handling"""
    
    print("🧪 Testing Enhanced Username Conflict Handling")
    print("=" * 60)
    
    # Initialize the orchestrator
    orchestrator = GmailRegistrationOrchestrator()
    
    # Initialize the system
    if not await orchestrator.initialize():
        print("❌ Failed to initialize orchestrator")
        return
    
    print("✅ Orchestrator initialized successfully")
    
    # Test with a single account registration
    print("\n🚀 Starting test registration...")
    result = await orchestrator.register_single_account(account_id=999)
    
    print("\n📊 Registration Result:")
    print("=" * 40)
    print(f"Success: {result.get('success', False)}")
    print(f"Email: {result.get('email', 'N/A')}")
    
    # Display username conflict information
    username_conflicts = result.get('username_conflicts', {})
    if username_conflicts:
        print(f"\n🔄 Username Conflict Information:")
        print(f"   Original Username: {username_conflicts.get('original_username', 'N/A')}")
        print(f"   Final Username: {username_conflicts.get('final_username', 'N/A')}")
        print(f"   Username Changed: {username_conflicts.get('username_changed', False)}")
        print(f"   Total Conflicts: {username_conflicts.get('total_conflicts', 0)}")
        
        if username_conflicts.get('conflict_history'):
            print(f"\n📝 Conflict Resolution History:")
            for i, conflict in enumerate(username_conflicts['conflict_history'], 1):
                print(f"   Conflict {i}:")
                print(f"      Method: {conflict.get('resolution_method', 'unknown')}")
                print(f"      Suggestions Found: {conflict.get('suggestions_found', 0)}")
                if conflict.get('selected_suggestion'):
                    print(f"      Selected: {conflict.get('selected_suggestion')}")
                if conflict.get('fallback_username'):
                    print(f"      Fallback: {conflict.get('fallback_username')}")
    
    if result.get('error'):
        print(f"\n❌ Error: {result.get('error')}")
        print(f"   Error Type: {result.get('error_type', 'Unknown')}")
    
    print(f"\n⏱️  Duration: {result.get('duration_seconds', 0):.1f} seconds")
    
    # Show log file location
    print(f"\n📄 Detailed logs saved to: username_conflict_test.log")
    print(f"📸 Screenshots saved to: screenshots/account_999_*.png")

def main():
    """Main function to run the test"""
    try:
        asyncio.run(test_username_conflict_handling())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logging.error(f"Test failed: {e}", exc_info=True)

if __name__ == "__main__":
    main()
