"""
Configuration module for Gmail Auto Registration Project
Handles all settings, environment variables, and project configurations.
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from constants import Timeouts, Browser, Localization, UserData, Paths

# Load environment variables
load_dotenv()

@dataclass
class ProxyConfig:
    """Proxy configuration dataclass"""
    server: str
    username: str
    password: str
    
    def to_dict(self) -> Dict[str, str]:
        return {
            "server": self.server,
            "username": self.username, 
            "password": self.password
        }

class Config:
    """Main configuration class for Gmail Auto Registration"""
    
    # ==============================================
    # API Keys & LLM Configuration
    # ==============================================
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    GROK_API_KEY = os.getenv('GROK_API_KEY')
    
    # Default LLM model
    DEFAULT_LLM_MODEL = os.getenv('DEFAULT_LLM_MODEL', 'gpt-4o-mini')
    if GOOGLE_API_KEY and not (OPENAI_API_KEY or ANTHROPIC_API_KEY):
        DEFAULT_LLM_MODEL = os.getenv('DEFAULT_LLM_MODEL', 'gemini-pro')
    
    # ==============================================
    # Browser Configuration
    # ==============================================
    HEADLESS_MODE = os.getenv('HEADLESS_MODE', 'false').lower() == 'true'
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', str(Timeouts.BROWSER_DEFAULT)))
    SLOW_MO_DELAY = int(os.getenv('SLOW_MO_DELAY', '200'))
    
    # Viewport configurations
    DEFAULT_VIEWPORT_WIDTH = int(os.getenv('DEFAULT_VIEWPORT_WIDTH', str(Browser.DEFAULT_VIEWPORT["width"])))
    DEFAULT_VIEWPORT_HEIGHT = int(os.getenv('DEFAULT_VIEWPORT_HEIGHT', str(Browser.DEFAULT_VIEWPORT["height"])))
    
    # Common viewport sizes for random selection
    VIEWPORT_SIZES = Browser.VIEWPORT_SIZES
    
    # US Timezones for random selection
    US_TIMEZONES = Localization.US_TIMEZONES
    
    # ==============================================
    # Proxy Configuration
    # ==============================================
    USE_PROXY = os.getenv('USE_PROXY', 'false').lower() == 'true'
    PROXY_ROTATION = os.getenv('PROXY_ROTATION', 'true').lower() == 'true'
    PROXY_TEST_TIMEOUT = int(os.getenv('PROXY_TEST_TIMEOUT', str(Timeouts.PROXY_TEST)))
    
    # Proxy list loaded from .env file
    PROXY_LIST: List[ProxyConfig] = []
    _proxies_json = os.getenv('PROXIES_JSON', '[]')
    try:
        _proxy_data = json.loads(_proxies_json)
        if isinstance(_proxy_data, list):
            PROXY_LIST = [ProxyConfig(**p) for p in _proxy_data]
    except json.JSONDecodeError:
        print("⚠️ WARNING: Invalid format for PROXIES_JSON in .env file. Skipping proxy loading.")
    
    # ==============================================
    # Registration Settings
    # ==============================================
    MAX_CONCURRENT_REGISTRATIONS = int(os.getenv('MAX_CONCURRENT_REGISTRATIONS', '2'))
    DELAY_BETWEEN_REGISTRATIONS = int(os.getenv('DELAY_BETWEEN_REGISTRATIONS', str(Timeouts.BETWEEN_REGISTRATIONS)))
    MAX_RETRY_ATTEMPTS = int(os.getenv('MAX_RETRY_ATTEMPTS', '3'))
    
    # Registration timeouts
    NAVIGATION_TIMEOUT = Timeouts.NAVIGATION
    FORM_FILL_TIMEOUT = Timeouts.FORM_FILL
    CAPTCHA_TIMEOUT = Timeouts.CAPTCHA_SOLVE
    VERIFICATION_TIMEOUT = Timeouts.VERIFICATION
    
    # ==============================================
    # Captcha Configuration
    # ==============================================
    CAPTCHA_SERVICE = os.getenv('CAPTCHA_SERVICE', 'none')
    TWOCAPTCHA_API_KEY = os.getenv('TWOCAPTCHA_API_KEY')
    ANTICAPTCHA_API_KEY = os.getenv('ANTICAPTCHA_API_KEY')
    
    # ==============================================
    # Logging Configuration
    # ==============================================
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    BROWSER_USE_LOGGING_LEVEL = os.getenv('BROWSER_USE_LOGGING_LEVEL', 'debug')
    SAVE_SCREENSHOTS = os.getenv('SAVE_SCREENSHOTS', 'true').lower() == 'true'
    SAVE_PAGE_HTML = os.getenv('SAVE_PAGE_HTML', 'false').lower() == 'true'
    SAVE_CONVERSATIONS = os.getenv('SAVE_CONVERSATIONS', 'true').lower() == 'true'
    
    # ==============================================
    # Data Storage Paths
    # ==============================================
    PROJECT_ROOT = Path(__file__).parent
    DATA_DIR = PROJECT_ROOT / os.getenv('DATA_DIR', Paths.DIRECTORIES["data"])
    LOGS_DIR = PROJECT_ROOT / os.getenv('LOGS_DIR', Paths.DIRECTORIES["logs"])
    SCREENSHOTS_DIR = PROJECT_ROOT / os.getenv('SCREENSHOTS_DIR', Paths.DIRECTORIES["screenshots"])
    CONVERSATIONS_DIR = PROJECT_ROOT / Paths.DIRECTORIES["conversations"]
    ACCOUNTS_DIR = PROJECT_ROOT / Paths.DIRECTORIES["accounts"]
    
    # Database files
    ACCOUNTS_DB = DATA_DIR / os.getenv('ACCOUNTS_DB', Paths.FILES["accounts_db"])
    ANALYTICS_DB = DATA_DIR / Paths.FILES["analytics_db"]
    
    # ==============================================
    # Anti-Detection Settings
    # ==============================================
    RANDOM_USER_AGENTS = os.getenv('RANDOM_USER_AGENTS', 'true').lower() == 'true'
    RANDOM_VIEWPORTS = os.getenv('RANDOM_VIEWPORTS', 'true').lower() == 'true'
    STEALTH_MODE = os.getenv('STEALTH_MODE', 'true').lower() == 'true'
    HUMAN_DELAYS = os.getenv('HUMAN_DELAYS', 'true').lower() == 'true'
    
    # Human behavior settings  
    MIN_TYPING_DELAY = Timeouts.HUMAN_TYPING["min"]
    MAX_TYPING_DELAY = Timeouts.HUMAN_TYPING["max"]
    MIN_ACTION_DELAY = Timeouts.HUMAN_ACTION["min"]
    MAX_ACTION_DELAY = Timeouts.HUMAN_ACTION["max"]
    MISTAKE_RATE = 0.05
    
    # ==============================================
    # User Data Generation
    # ==============================================
    AMERICAN_FIRST_NAMES = UserData.AMERICAN_FIRST_NAMES_MALE + UserData.AMERICAN_FIRST_NAMES_FEMALE
    AMERICAN_LAST_NAMES = UserData.AMERICAN_LAST_NAMES
    
    # US Area codes for phone generation
    US_AREA_CODES = UserData.US_AREA_CODES
    
    # ==============================================
    # Success Tracking & Analytics
    # ==============================================
    ENABLE_ANALYTICS = os.getenv('ENABLE_ANALYTICS', 'true').lower() == 'true'
    WEBHOOK_URL = os.getenv('WEBHOOK_URL')
    NOTIFICATION_EMAIL = os.getenv('NOTIFICATION_EMAIL')
    
    # ==============================================
    # Chrome Arguments for Anti-Detection
    # ==============================================
    CHROME_ARGS = Browser.CHROME_ARGS
    
    @classmethod
    def ensure_directories(cls):
        """Create necessary directories if they don't exist"""
        directories = [
            cls.DATA_DIR,
            cls.LOGS_DIR, 
            cls.SCREENSHOTS_DIR,
            cls.CONVERSATIONS_DIR,
            cls.ACCOUNTS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        print(f"✅ Created project directories:")
        for directory in directories:
            print(f"   📁 {directory}")
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate the configuration to ensure the application can run."""
        print("⚙️  Validating configuration...")
        errors = []

        # Check for at least one LLM API key
        if not any([cls.OPENAI_API_KEY, cls.ANTHROPIC_API_KEY, cls.GOOGLE_API_KEY, cls.DEEPSEEK_API_KEY, cls.GROK_API_KEY]):
            errors.append("No LLM API key provided (e.g., OPENAI_API_KEY, GOOGLE_API_KEY).")

        # Check Captcha config if service is enabled
        if cls.CAPTCHA_SERVICE == '2captcha' and not cls.TWOCAPTCHA_API_KEY:
            errors.append("2Captcha is enabled, but TWOCAPTCHA_API_KEY is missing.")
        if cls.CAPTCHA_SERVICE == 'anticaptcha' and not cls.ANTICAPTCHA_API_KEY:
            errors.append("Anti-Captcha is enabled, but ANTICAPTCHA_API_KEY is missing.")

        # Check Proxy config
        if cls.USE_PROXY and not cls.PROXY_LIST:
            errors.append("USE_PROXY is true, but PROXY_LIST is empty or invalid in .env.")

        if errors:
            print("🚨 Configuration Validation Errors:")
            for error in errors:
                print(f"   ❌ {error}")
            return False
        
        print("✅ Configuration is valid.")
        return True
    
    @classmethod
    def print_config_summary(cls):
        """Print configuration summary"""
        print("\n🔧 Gmail Auto Registration Configuration:")
        print(f"   🤖 LLM Model: {cls.DEFAULT_LLM_MODEL}")
        print(f"   🌐 Headless Mode: {cls.HEADLESS_MODE}")
        print(f"   🔄 Use Proxy: {cls.USE_PROXY}")
        print(f"   📊 Max Concurrent: {cls.MAX_CONCURRENT_REGISTRATIONS}")
        print(f"   ⏱️  Delay Between: {cls.DELAY_BETWEEN_REGISTRATIONS}s")
        print(f"   📸 Save Screenshots: {cls.SAVE_SCREENSHOTS}")
        print(f"   📝 Log Level: {cls.LOG_LEVEL}")
        print(f"   🎭 Stealth Mode: {cls.STEALTH_MODE}")

# Initialize configuration on import
if __name__ == "__main__":
    Config.ensure_directories()
    Config.validate_config()
    Config.print_config_summary() 