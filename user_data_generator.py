"""
User Data Generator Module
Generates realistic American user data for Gmail registration including names, usernames, passwords, phone numbers, etc.
"""

import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, List
from config import Config
from constants import UserData, Localization


class UserDataGenerator:
    """Tạo dữ liệu user thực tế cho đăng ký Gmail"""
    
    @classmethod
    def generate_user_info(cls) -> Dict[str, Any]:
        """
        Tạo thông tin user hoàn chỉnh với dữ liệu realistic
        
        Returns:
            Dict chứa tất cả thông tin user cần thiết
        """
        # Random gender để chọn tên phù hợp
        gender = random.choice(["Male", "Female"])
        
        if gender == "Male":
            first_name = random.choice(UserData.AMERICAN_FIRST_NAMES_MALE)
        else:  # Female
            first_name = random.choice(UserData.AMERICAN_FIRST_NAMES_FEMALE)
            
        last_name = random.choice(UserData.AMERICAN_LAST_NAMES)
        
        # Tạo username realistic với các pattern khác nhau
        username = cls._generate_username(first_name, last_name)
        
        # Tạo password mạnh
        password = cls._generate_strong_password()
        
        # Tạo ngày sinh realistic
        current_year = datetime.now().year
        min_birth_year = current_year - UserData.AGE_RANGE["max"]
        max_birth_year = current_year - UserData.AGE_RANGE["min"]
        birth_year = random.randint(min_birth_year, max_birth_year)
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)  # Tránh lỗi ngày không tồn tại
        
        # Format birth_date
        birth_date = f"{birth_year}-{birth_month:02d}-{birth_day:02d}"
        
        # Phone number Mỹ
        phone = cls._generate_us_phone()
        
        # Recovery email
        recovery_email = cls._generate_recovery_email(first_name, last_name)
        
        # Additional info
        city = random.choice(UserData.US_CITIES)
        
        return {
            "first_name": first_name,
            "last_name": last_name,
            "full_name": f"{first_name} {last_name}",
            "username": username,
            "password": password,
            "birth_date": birth_date,
            "birth_year": birth_year,
            "birth_month": birth_month, 
            "birth_day": birth_day,
            "phone": phone,
            "recovery_email": recovery_email,
            "gender": gender,
            "city": city,
            "timezone": random.choice(Localization.US_TIMEZONES),
            "generated_at": datetime.now().isoformat()
        }
    
    @classmethod
    def _generate_username(cls, first_name: str, last_name: str) -> str:
        """
        Tạo username realistic với các pattern khác nhau
        
        Args:
            first_name: Tên
            last_name: Họ
            
        Returns:
            Username string
        """
        first_lower = first_name.lower()
        last_lower = last_name.lower()
        
        # Gmail username patterns (only letters, numbers, and periods allowed)
        patterns = [
            f"{first_lower}{last_lower}",
            f"{first_lower}.{last_lower}",
            f"{first_lower}{last_lower}{random.randint(1, 999)}",
            f"{first_lower}.{last_lower}{random.randint(1, 99)}",
            f"{first_lower[0]}{last_lower}",
            f"{first_lower}{last_lower[0]}",
            f"{first_lower}{random.randint(1980, 2005)}",
            f"{last_lower}{first_lower}",
            f"{first_lower}{last_lower}{random.randint(10, 99)}",
            f"{first_lower[:3]}{last_lower[:3]}{random.randint(1, 999)}",
            f"{first_lower}.{last_lower[:3]}",
            f"{first_lower[:3]}.{last_lower}",
            f"{first_lower}{last_lower}{random.randint(100, 999)}",
            f"{first_lower}.{last_lower}.{random.randint(1, 99)}"
        ]
        
        return random.choice(patterns)
    
    @classmethod
    def _generate_strong_password(cls, length: int = None) -> str:
        """
        Tạo password mạnh thỏa mãn requirements của Gmail
        
        Args:
            length: Độ dài password (default từ config)
            
        Returns:
            Password string
        """
        if length is None:
            length = UserData.PASSWORD_CONFIG["default_length"]
        
        if length < UserData.PASSWORD_CONFIG["min_length"]:
            length = UserData.PASSWORD_CONFIG["min_length"]
            
        # Đảm bảo có ít nhất 1 ký tự mỗi loại
        password_chars = []
        
        # Ít nhất 1 chữ hoa
        if UserData.PASSWORD_CONFIG["include_uppercase"]:
            password_chars.append(random.choice(string.ascii_uppercase))
        
        # Ít nhất 1 chữ thường  
        if UserData.PASSWORD_CONFIG["include_lowercase"]:
            password_chars.append(random.choice(string.ascii_lowercase))
        
        # Ít nhất 1 số
        if UserData.PASSWORD_CONFIG["include_digits"]:
            password_chars.append(random.choice(string.digits))
        
        # Ít nhất 1 ký tự đặc biệt (Gmail-friendly)
        if UserData.PASSWORD_CONFIG["include_special"]:
            password_chars.append(random.choice(UserData.PASSWORD_CONFIG["special_chars"]))
        
        # Fill còn lại với random chars
        all_chars = ""
        if UserData.PASSWORD_CONFIG["include_uppercase"]:
            all_chars += string.ascii_uppercase
        if UserData.PASSWORD_CONFIG["include_lowercase"]:
            all_chars += string.ascii_lowercase
        if UserData.PASSWORD_CONFIG["include_digits"]:
            all_chars += string.digits
        if UserData.PASSWORD_CONFIG["include_special"]:
            all_chars += UserData.PASSWORD_CONFIG["special_chars"]
            
        for _ in range(length - len(password_chars)):
            password_chars.append(random.choice(all_chars))
        
        # Shuffle để tránh pattern predictable
        random.shuffle(password_chars)
        
        return ''.join(password_chars)
    
    @classmethod
    def _generate_us_phone(cls) -> str:
        """
        Tạo số điện thoại Mỹ realistic
        
        Returns:
            Phone number string (format: +1XXXXXXXXXX)
        """
        # Chọn area code
        area_code = random.choice(UserData.US_AREA_CODES)
        
        # Tạo 7 digits còn lại (XXX-XXXX)
        # Digit đầu của exchange code không thể là 0 hoặc 1
        exchange_first = random.randint(2, 9)
        exchange_rest = random.randint(10, 99)
        
        # 4 digits cuối
        last_four = random.randint(1000, 9999)
        
        return f"+1{area_code}{exchange_first}{exchange_rest}{last_four}"
    
    @classmethod
    def _generate_recovery_email(cls, first_name: str, last_name: str) -> str:
        """
        Tạo recovery email realistic
        
        Args:
            first_name: Tên
            last_name: Họ
            
        Returns:
            Recovery email string
        """
        first_lower = first_name.lower()
        last_lower = last_name.lower()
        domain = random.choice(UserData.COMMON_EMAIL_DOMAINS)
        
        # Patterns cho recovery email
        patterns = [
            f"{first_lower}.{last_lower}",
            f"{first_lower}_{last_lower}",
            f"{first_lower}{last_lower}",
            f"{first_lower}.{last_lower}{random.randint(1, 99)}",
            f"{first_lower}_{last_lower}_{random.randint(1, 99)}",
            f"{first_lower}{random.randint(1980, 2000)}",
            f"{first_lower[0]}.{last_lower}",
            f"{first_lower}.{last_lower[:3]}"
        ]
        
        username = random.choice(patterns)
        return f"{username}@{domain}"
    
    @classmethod
    def generate_batch_users(cls, count: int) -> List[Dict[str, Any]]:
        """
        Tạo nhiều user data cùng lúc
        
        Args:
            count: Số lượng user cần tạo
            
        Returns:
            List các user data dict
        """
        users = []
        used_usernames = set()
        
        for i in range(count):
            while True:
                user_data = cls.generate_user_info()
                
                # Đảm bảo username unique
                if user_data['username'] not in used_usernames:
                    used_usernames.add(user_data['username'])
                    users.append(user_data)
                    break
        
        return users
    
    @classmethod
    def validate_user_data(cls, user_data: Dict[str, Any]) -> bool:
        """
        Validate user data có đúng format không
        
        Args:
            user_data: Dict chứa user data
            
        Returns:
            bool: True nếu valid
        """
        required_fields = [
            'first_name', 'last_name', 'username', 'password',
            'birth_year', 'birth_month', 'birth_day', 'phone'
        ]
        
        # Kiểm tra required fields
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                return False
        
        # Validate password strength
        password = user_data['password']
        if len(password) < 8:
            return False
            
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*" for c in password)
        
        if not all([has_upper, has_lower, has_digit, has_special]):
            return False
        
        # Validate birth date
        try:
            year = user_data['birth_year']
            month = user_data['birth_month']
            day = user_data['birth_day']
            
            if not (1980 <= year <= 2005):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1 <= day <= 31):
                return False
                
        except (ValueError, TypeError):
            return False
        
        # Validate phone format
        phone = user_data['phone']
        if not phone.startswith('+1') or len(phone) != 12:
            return False
        
        return True
    
    @classmethod
    def print_user_info(cls, user_data: Dict[str, Any]):
        """
        In thông tin user ra console để debug
        
        Args:
            user_data: Dict chứa user data
        """
        print(f"\n👤 Generated User Data:")
        print(f"   📛 Name: {user_data['full_name']}")
        print(f"   👤 Username: {user_data['username']}")
        print(f"   🔒 Password: {user_data['password']}")
        print(f"   📅 DOB: {user_data['birth_month']}/{user_data['birth_day']}/{user_data['birth_year']}")
        print(f"   📱 Phone: {user_data['phone']}")
        print(f"   📧 Recovery: {user_data['recovery_email']}")
        print(f"   🌍 City: {user_data['city']}")
        print(f"   ⏰ Timezone: {user_data['timezone']}")


# Utility functions
def generate_single_user() -> Dict[str, Any]:
    """Convenience function để tạo 1 user"""
    return UserDataGenerator.generate_user_info()


def generate_multiple_users(count: int) -> List[Dict[str, Any]]:
    """Convenience function để tạo nhiều users"""
    return UserDataGenerator.generate_batch_users(count)


# For testing
if __name__ == "__main__":
    # Test generation
    print("🧪 Testing User Data Generation...")
    
    # Generate single user
    user = UserDataGenerator.generate_user_info()
    UserDataGenerator.print_user_info(user)
    
    # Validate
    is_valid = UserDataGenerator.validate_user_data(user)
    print(f"\n✅ Validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # Generate batch
    print(f"\n🎯 Generating batch of 3 users...")
    users = UserDataGenerator.generate_batch_users(3)
    
    for i, user in enumerate(users, 1):
        print(f"\n--- User {i} ---")
        print(f"Name: {user['full_name']}")
        print(f"Username: {user['username']}")
        print(f"Email: {user['username']}@gmail.com")
        print(f"Valid: {UserDataGenerator.validate_user_data(user)}") 